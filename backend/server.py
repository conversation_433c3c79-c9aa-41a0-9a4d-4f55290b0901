from contextlib import asynccontextmanager
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
import asyncio

from backend.app.api import api_router
from backend.app.db.init_db import create_database_tables

from backend.app.db.database import async_engine
from backend.app.config.env import app_settings, redis_settings
from backend.app.core.utils import Utils
from backend.app.db.redis import get_redis_connection, redis_pool
from backend.app.services.init_data_service import initialize_app
from backend.app.db.session import get_async_transaction_session


# 在应用启动前设置日志
Utils.setup_logging()


@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时的初始化任务
    logger.info(f"Starting application '{app_settings.app_name}' initialization...")

    # 1. 创建数据库表
    await create_database_tables()


    # 2. 设置Redis客户端到应用状态

    async for redis_client in get_redis_connection():
        app.state.redis = redis_client  # type: ignore
        logger.info(f"Redis客户端已配置: {redis_settings.redis_host}:{redis_settings.redis_port}")
        break


    # 3. 等待一小段时间确保数据库初始化完全完成
    await asyncio.sleep(0.1)

    # 4. 执行应用初始化（包括管理员用户初始化）
    # 使用事务会话确保初始化操作的原子性
    async with get_async_transaction_session() as db:
        await initialize_app(db)  # 传递数据库会话
        await db.commit()  # 手动提交事务
        logger.info("Application initialization transaction committed")

    logger.success(f"{app_settings.app_name} started successfully")

    yield

    try:
        # 关闭 db 数据库引擎
        logger.info("正在关闭数据库引擎...")
        await async_engine.dispose()
        logger.info("数据库引擎已关闭")

        # Redis连接由连接池自动管理，无需手动关闭
        logger.info("Redis连接池将自动关闭")


    except Exception as e:
        logger.error(f"关闭{app_settings.app_name}时出错: {str(e)}")

    logger.info(f"{app_settings.app_name}关闭成功")

app = FastAPI(
    title=app_settings.app_name,
    version=app_settings.app_version,
    lifespan=lifespan,

)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=app_settings.app_cors_origins,
    allow_credentials=app_settings.app_cors_credentials,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(api_router)


@app.get("/")
async def root():
    return {"message": "Hello World"}


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "message": "Service is running"}


