"""
Authentication Module Router
"""
from fastapi import APIRouter

# Import all auth-related routers
from .login import router as login_router
from .token import router as token_router
from .users import router as users_router
from .roles import router as roles_router
from .permissions import router as permissions_router
from .api_scopes import router as api_scopes_router
from .init_data import router as init_data_router

# Create authentication module main router
auth_router = APIRouter()

# Register sub-module routes with appropriate tags and prefixes
auth_router.include_router(
    login_router,
    tags=["Authentication"]
)

auth_router.include_router(
    token_router,
    prefix="/tokens",
    tags=["Token Management"]
)

auth_router.include_router(
    users_router,
    prefix="/users",
    tags=["User Management"]
)

auth_router.include_router(
    roles_router,
    prefix="/roles",
    tags=["Role Management"]
)

auth_router.include_router(
    permissions_router,
    prefix="/permissions",
    tags=["Permission Management"]
)

auth_router.include_router(
    api_scopes_router,
    prefix="/api-scopes",
    tags=["API Scopes Management"]
)

auth_router.include_router(
    init_data_router,
    prefix="/init",
    tags=["System Initialization"]
)
