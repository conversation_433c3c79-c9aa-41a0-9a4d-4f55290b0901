"""
应用程序枚举定义模块

包含应用程序中使用的各种枚举类型定义，集中管理以确保一致性。
"""

from enum import Enum


class DownloadStatus(str, Enum):
    """下载状态枚举"""
    PENDING = "pending"      # 待下载
    DOWNLOADING = "downloading"  # 下载中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败
    SKIPPED = "skipped"      # 跳过

class RoleType(str, Enum):
    """角色类型枚举"""
    ADMIN = ("admin", "系统管理员")
    USER = ("user", "普通用户")
    TEST = ("test", "测试用户")
    # EDITOR = ("editor", "编辑者")
    # VIEWER = ("viewer", "查看者")

    def __new__(cls, value, description):
        obj = str.__new__(cls, value)
        obj._value_ = value
        obj.description = description
        return obj

    def __str__(self):
        return self.value

class PermissionType(str, Enum):
    """权限类型枚举"""
    MANAGE_USERS = ("manage_users", "管理用户")
    MANAGE_ROLES = ("manage_roles", "管理角色")
    MANAGE_PERMISSIONS = ("manage_permissions", "管理权限")
    MANAGE_TOKENS = ("manage_tokens", "管理令牌")
    MANAGE_API_PERMISSIONS = ("manage_api_permissions", "管理API权限")
    DOWNLOAD_FILE = ("download_file", "下载文件")
    UPLOAD_FILE = ("upload_file", "上传文件")
    WRITE_DATABASE = ("write_database", "写入数据库")
    READ_DATABASE = ("read_database", "读取数据库")
    DELETE_RECORD = ("delete_record", "删除记录")
    CREATE_USER = ("create_user", "创建用户")
    UPDATE_PROFILE = ("update_profile", "更新个人资料")
    MODIFY_SETTINGS = ("modify_settings", "修改设置")
    CREATE_RECORD = ("create_record", "创建记录")
    ADMINISTRATOR = ("administrator", "超级管理员")

    def __new__(cls, value, description):
        obj = str.__new__(cls, value)
        obj._value_ = value
        obj.description = description
        return obj

    def __str__(self):
        return self.value


class TokenType(str, Enum):
    ACCESS = "access"
    REFRESH = "refresh"
    API = "api"