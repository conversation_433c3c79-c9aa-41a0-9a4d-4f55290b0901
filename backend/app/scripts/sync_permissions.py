#!/usr/bin/env python3
"""
权限同步管理脚本

使用方法:
python -m app.scripts.sync_permissions sync    # 同步权限到数据库
python -m app.scripts.sync_permissions validate # 验证配置文件
python -m app.scripts.sync_permissions export   # 导出数据库配置到YAML
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger
from backend.app.services.sync_service import (
    sync_api_permissions, 
    validate_permission_config, 
    export_permissions_to_yaml
)
from backend.app.core.deps import get_async_session


async def sync_command():
    """同步权限配置到数据库"""
    logger.info("开始同步API权限配置...")
    
    try:
        async for db in get_async_session():
            stats = await sync_api_permissions(db)
            
            print("=" * 50)
            print("权限同步完成!")
            print(f"创建: {stats['created']} 个")
            print(f"更新: {stats['updated']} 个")
            print(f"停用: {stats['deactivated']} 个")
            print(f"跳过: {stats['skipped']} 个")
            print("=" * 50)
            
            break  # 只需要第一个数据库会话
            
    except Exception as e:
        logger.error(f"同步失败: {e}")
        sys.exit(1)


async def validate_command():
    """验证权限配置文件"""
    logger.info("验证权限配置文件...")
    
    try:
        errors = await validate_permission_config()
        
        if errors:
            print("=" * 50)
            print("配置文件验证失败!")
            for error in errors:
                print(f"❌ {error}")
            print("=" * 50)
            sys.exit(1)
        else:
            print("=" * 50)
            print("✅ 配置文件验证通过!")
            print("=" * 50)
            
    except Exception as e:
        logger.error(f"验证失败: {e}")
        sys.exit(1)


async def export_command():
    """导出数据库配置到YAML文件"""
    logger.info("导出数据库权限配置...")
    
    try:
        async for db in get_async_session():
            await export_permissions_to_yaml(db)
            
            print("=" * 50)
            print("✅ 权限配置已导出到 app/config/api_permissions_export.yml")
            print("=" * 50)
            
            break  # 只需要第一个数据库会话
            
    except Exception as e:
        logger.error(f"导出失败: {e}")
        sys.exit(1)


def show_help():
    """显示帮助信息"""
    print("""
权限管理工具

使用方法:
    python -m app.scripts.sync_permissions <command>

可用命令:
    sync        同步YAML配置到数据库
    validate    验证YAML配置文件格式
    export      导出数据库配置到YAML文件
    help        显示此帮助信息

示例:
    python -m app.scripts.sync_permissions sync
    python -m app.scripts.sync_permissions validate
    python -m app.scripts.sync_permissions export
    """)


async def main():
    """主函数"""
    if len(sys.argv) < 2:
        show_help()
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "sync":
        await sync_command()
    elif command == "validate":
        await validate_command()
    elif command == "export":
        await export_command()
    elif command in ["help", "-h", "--help"]:
        show_help()
    else:
        print(f"未知命令: {command}")
        show_help()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
