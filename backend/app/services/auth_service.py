from datetime import datetime, timedelta, timezone
from uuid import uuid4
from typing import Optional

from passlib.context import Crypt<PERSON>ontext
from jose import jwt, JWTError, ExpiredSignatureError
from fastapi import Depends, HTTPException, status,  Cookie
from fastapi.security import OAuth2PasswordBearer
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from loguru import logger
import json

from backend.app.config.env import jwt_settings
from backend.app.models import User, Role
from backend.app.core.deps import AsyncSessionDep
from backend.app.config.enums import TokenType
from backend.app.api.v1.schemas.user import TokenValidationResponse, AuthContext
from backend.app.repo.user_repository import UserRepository, ApiScopesRepository
from backend.app.repo.token_repository import RefreshTokenRepository, get_refresh_token_repository


# 使用 passlib 库加密密码, deprecated="auto" 不使用废弃的加密方法
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="v1/auth/login")
security = HTTPBearer()


class AuthService:

    # 加密用户名的密码
    @staticmethod
    def get_password_hash(password: str):
        return pwd_context.hash(password)

    # 验证密码
    @staticmethod
    def verify_password(plain, hashed):
        try:
            logger.debug(f"plain: {plain}, hashed: {hashed}")
            return pwd_context.verify(plain, hashed)

        except Exception as e:
            logger.error(f"验证密码时出错: {e}")
            return False

    # logger.info(verify_password("Heytime01!","$2b$12$JZZD/JyiK2tXBfLMxiFKq.oGoDzncNiSNPNR9QQXPjTzJxbhcbriG"))

    # 注册密码时候就用hashed_password = pwd_context.hash("password")加密

    # 验证用户
    @staticmethod
    async def verify_user(username, password, db):
        """
        验证用户名和密码

        Args:
            username: 用户名
            password: 明文密码
            db: 数据库会话

        Returns:
            User: 验证成功返回用户对象，验证失败返回None
        """
        try:
            # 查询用户
            logger.info(f"验证用户: {username}")
            user = await UserRepository(db).get_one_by_username(username)
            logger.info(f"获取用户: {user}")

            if user and AuthService.verify_password(password, user.password):
                logger.success(f"用户验证成功: {user}")
                return user
            else:
                logger.error(f"用户验证失败: {username}")
                return None
        except Exception as e:
            logger.error(f"验证用户时出错: {e}")
            return None

    # ==================== 自动刷新依赖函数 ====================

    @staticmethod
    async def get_current_user(*,
            access_token: str,
            token_repo,
            refresh_token: Optional[str] = None,
    ) -> str:
        """
        获取当前用户信息（不使用Depends的版本）

        这个方法可以在普通方法中直接调用，不需要FastAPI的依赖注入

        Args:
            access_token: Access Token
            token_repo: TokenRepository实例
            refresh_token: Refresh Token (可选)

        Returns:
            str: 用户名

        Raises:
            HTTPException: 认证失败时抛出 401 错误
        """

        # 验证 access token
        access_token_status = TokenService.verify_access_token(access_token)

        if access_token_status["valid"]:
            username = access_token_status["username"]
            logger.success(
                f"Access token for {username} is valid, expires in {access_token_status["expire_seconds"]} seconds")
            return username
        else:
            username = access_token_status.get("username", "unknown")
            logger.debug(f"Access token invalid or expired (user: {username}). Trying to refresh...")

        # Access token 无效或不存在，尝试使用 refresh token
        if not refresh_token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="No valid access token or refresh token provided",
                headers={"WWW-Authenticate": "Bearer"},
            )

        try:
            # 验证 refresh token 并生成新的 access token
            token_info = await TokenService.verify_refresh_token(refresh_token, token_repo)
            new_access_token = TokenService.create_access_token({"sub": token_info["username"]})

            # 获取新 token 的信息
            new_payload = TokenService.decode_token(new_access_token)
            new_jti = new_payload.get("token_id")
            exp = new_payload.get("exp")
            expire_seconds = exp - datetime.now(timezone.utc).timestamp() if exp else 0

            logger.info(
                f"Access token refreshed for user: {token_info['username']}, new JTI: {new_jti}, expire in {expire_seconds} seconds")

            return token_info["username"]

        except HTTPException as e:
            # Refresh token 也无效
            logger.warning(f"Both access and refresh tokens invalid: {e.detail}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired tokens, please login again",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except Exception as e:
            logger.error(f"Token refresh process failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication service error"
            )

    # 通过用户名获取所有的权限
    @staticmethod
    async def get_user_permission(*, username: str, db):
        """
        获取用户的所有权限

        Args:
            username: 用户名
            db: 数据库会话

        Returns:
            List[Permission]: 用户拥有的权限列表
        """
        try:
            # 查询用户，并预加载角色和权限
            # 使用 selectinload 来预加载关联的  Role 和  Permission 数据，减少数据库查询次数

            stmt = select(User).where(User.username == username).options(
                selectinload(User.roles).selectinload(Role.permissions)
            )
            result = await db.execute(stmt)
            #  如果使用joinedload, 需要使用 unique() 确保结果不重复
            user = result.scalar_one_or_none()

            if user is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid authentication credentials",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            # 检查用户是否有关联的角色
            if not user.roles:
                logger.warning(f"用户 {username} 没有关联的角色")
                return []  # 返回空列表表示没有角色关联

            # 收集用户所有角色的所有权限
            all_permissions = []
            for role in user.roles:
                if role.permissions:
                    # 将角色的所有权限添加到总权限列表中
                    all_permissions.extend(role.permissions)
                else:
                    logger.warning(f"角色 {role.name} 没有关联的权限")

            # 去重，其中 perm.id 作为字典的键，perm 作为值。
            unique_permissions = list({perm.id: perm for perm in all_permissions}.values())

            logger.info(f"用户 {username} 的权限: {[perm.name for perm in unique_permissions]}")
            return unique_permissions

        except Exception as e:
            logger.error(f"获取用户权限时出错: {e}")
            return []


class TokenService:

    # @staticmethod
    # def create_api_token(username: str, selected_endpoints: list[str], expires_hours: int = 720):
    #     """创建API Token - 核心逻辑"""
    #
    #     # 1. 验证用户对端点的权限
    #     valid_endpoints = []
    #     required_permissions = set()
    #
    #     for endpoint in selected_endpoints:
    #         endpoint_perms = api_scopeS_MAP.get(endpoint, [])
    #         if user_has_permissions(username, endpoint_perms):
    #             valid_endpoints.append(endpoint)
    #             required_permissions.update(endpoint_perms)
    #         else:
    #             raise HTTPException(403, f"No permission for endpoint: {endpoint}")
    #
    #     # 2. 创建JWT payload
    #
    #     iat = datetime.now(timezone.utc)
    #     api_token_expire = iat + timedelta(hours=expires_hours)
    #
    #     jti = str(uuid4())
    #
    #     payload = {
    #         "token_id": jti,
    #         "exp": api_token_expire,
    #         "iat": iat,
    #         "sub": username,
    #         "type": "api",
    #         "endpoints": valid_endpoints,  # 端点白名单
    #         "scopes": list(required_permissions),  # 最小权限集
    #     }
    #
    #     # 3. 生成JWT Token
    #     token = jwt.encode(payload, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    #
    #     # 4. 存储Token元数据到数据库
    #     #store_token_metadata(token_id, user_id, valid_endpoints, list(required_permissions))
    #
    #     return {"token": token, "token_id": jti}

    # 使用 jose 库生成 JWT 令牌
    @staticmethod
    def create_access_token(data: dict):
        to_encode = data.copy()

        # 设置过期时间和签发时间
        iat = datetime.now(timezone.utc)
        access_token_expire = iat + timedelta(minutes=jwt_settings.jwt_access_token_expire_time)

        # 生成唯一的 JTI
        jti = str(uuid4())

        to_encode.update({
            "exp": access_token_expire,
            "iat": iat,
            "type": TokenType.ACCESS.value,
            "token_id": jti
        })
        encoded_jwt = jwt.encode(to_encode, jwt_settings.jwt_secret_key, algorithm=jwt_settings.jwt_algorithm)

        return encoded_jwt

    @staticmethod
    def create_refresh_token(data: dict):
        to_encode = data.copy()

        # 设置过期时间
        iat = datetime.now(timezone.utc)
        refresh_token_expire = iat + timedelta(days=jwt_settings.jwt_refresh_token_expire_time)

        jti = str(uuid4())
        to_encode.update({"exp": refresh_token_expire,
                          "type": TokenType.REFRESH.value,
                          "iat": iat,
                          "token_id": jti
                          })
        encoded_jwt = jwt.encode(to_encode, jwt_settings.jwt_secret_key, algorithm=jwt_settings.jwt_algorithm)

        return encoded_jwt

    @staticmethod
    def get_token_type(token: str) -> str:
        if not token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Token not found",
                headers={"WWW-Authenticate": "Bearer"},
            )
        try:
            payload = TokenService.decode_token(token)

            # 验证 token 类型
            token_type = payload.get("type")
            if token_type not in TokenType.__members__.values():
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail=f"Invalid token type",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            return token_type
        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Invalid token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Invalid token, {e}",
                headers={"WWW-Authenticate": "Bearer"},
            )

    @staticmethod
    def verify_token_type(token: str, expected_type: str) -> dict:
        """
        验证 token 类型并返回 payload

        Args:
            token: JWT token 字符串
            expected_type: 期望的 token 类型 ("access" 或 "refresh")

        Returns:
            dict: token payload

        Raises:
            HTTPException: token 无效或类型不匹配时抛出异常
        """
        logger.info(
            f"开始验证 token，token 长度: {len(token) if token else 0}")

        if not token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"{expected_type.title()} token not found",
                headers={"WWW-Authenticate": "Bearer"},
            )

        try:
            payload = TokenService.decode_token(token)

            # 验证 token 类型
            token_type = payload.get("type")
            if token_type != expected_type:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail=f"Invalid token type, {expected_type} token required",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            return payload

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Token verification failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Invalid {expected_type} token",
                headers={"WWW-Authenticate": "Bearer"},
            )

    @staticmethod
    def decode_token(token: str):
        """
        解码 JWT token

        Args:
            token: JWT token 字符串

        Returns:
            dict: 解码后的 payload

        Raises:
            HTTPException: token 过期或无效时抛出异常
        """
        try:
            payload = jwt.decode(
                token,
                jwt_settings.jwt_secret_key,
                algorithms=[jwt_settings.jwt_algorithm]
            )
            return payload
        except ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token",
                headers={"WWW-Authenticate": "Bearer"},
            )

    @staticmethod
    def extract_token_from_header(authorization: str) -> Optional[str]:
        """
        从 Authorization header 中提取 token

        Args:
            authorization: Authorization header 值

        Returns:
            Optional[str]: 提取的 token，如果无效则返回 None
        """
        if not authorization:
            return None

        try:
            scheme, token = authorization.split()
            if scheme.lower() != "bearer":
                return None
            return token
        except ValueError:
            return None

    @staticmethod
    def verify_access_token(access_token: str) -> TokenValidationResponse:

        try:
            payload = TokenService.verify_token_type(access_token, "access")
            token_status = TokenService.check_token_status(payload)

            return TokenValidationResponse(
                valid=True,
                username=token_status["username"],
                jti=token_status["jti"],
                token=access_token,
                token_type=token_status["token_type"],
                expires_seconds=int(token_status["expire_seconds"]),
                message=f"{token_status["token_type"].capitalize()} token is valid"
            )

        except Exception as e:
            logger.error(f"Refresh token verification failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token",
                headers={"WWW-Authenticate": "Bearer"},
            )

    @staticmethod
    async def verify_refresh_token(refresh_token: str, token_repo) -> TokenValidationResponse:
        """
        验证 refresh token 并返回用户信息

        Args:
            refresh_token: refresh token 字符串
            token_repo: RefreshTokenRepository 实例

        Returns:
            dict: 包含用户名和JTI的字典

        Raises:
            HTTPException: token 无效时抛出异常
        """
        # 验证 token 类型和格式
        payload = TokenService.verify_token_type(refresh_token, "refresh")

        token_status = TokenService.check_token_status(payload)

        try:
            # 验证 Redis 中的 token
            stored_username = await token_repo.get_stored_refresh_token(token_status["jti"])
            if stored_username != token_status["username"]:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid refresh token",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            return TokenValidationResponse(
                valid=True,
                username=token_status["username"],
                jti=token_status["jti"],
                token=refresh_token,
                token_type=token_status["token_type"],
                expires_seconds=int(token_status["expire_seconds"]),
                message=f"{token_status["token_type"].capitalize()} token is valid"
            )

        except Exception as e:
            logger.error(f"Refresh token verification failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token",
                headers={"WWW-Authenticate": "Bearer"},
            )

    @staticmethod
    async def verify_api_token(api_token: str, db) -> TokenValidationResponse:
        payload = TokenService.verify_token_type(api_token, "api")
        token_status = TokenService.check_token_status(payload)

        # 验证用户是否存在
        user_repo = UserRepository(db)
        user = await user_repo.get_one_by_username(token_status["username"])
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not exist",
                headers={"WWW-Authenticate": "Bearer"},
            )

        token_scopes = payload.get("scopes", "").split()
        logger.debug(f"Token scopes: {token_scopes}")



        return TokenValidationResponse(
            valid=True,
            username=token_status["username"],
            jti=token_status["jti"],
            token=api_token,
            token_type=token_status["token_type"],
            scopes= token_scopes,
            expires_seconds=int(token_status["expire_seconds"]),
            message=f"{token_status["token_type"].capitalize()} token is valid"
        )




    @staticmethod
    async def revoke_refresh_token(response, refresh_token: str, token_repo) -> dict:
        """
        撤销用户的 refresh token

        Args:
            response: response object
            refresh_token: refresh token
            token_repo: token repository
        """
        try:
            token_info = await TokenService.verify_refresh_token(refresh_token, token_repo)
            # 撤销 token
            success = await token_repo.revoke_refresh_token(token_info["jti"])

            # 清除 cookie
            response.delete_cookie(key="refresh_token")

            if success:
                logger.info(f"Refresh token revoked for user: {token_info['username']}")
                return {
                    "success": True,
                    "jit": token_info["jti"]
                }
            else:
                return {
                    "success": False,
                    "jit": token_info["jti"]
                }

        except HTTPException:
            # 即使验证失败也要清除 cookie
            response.delete_cookie(key="refresh_token")
            raise
        except Exception as e:
            logger.error(f"撤销 refresh token 失败: {e}")
            # 即使出错也要清除 cookie
            response.delete_cookie(key="refresh_token")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to revoke refresh token"
            )

    @staticmethod
    async def refresh_user_tokens(username: str, old_jti: str, token_repo) -> dict:
        """
        刷新用户的 tokens

        Args:
            username: 用户名
            old_jti: 旧的 refresh token JTI
            token_repo: RefreshTokenRepository 实例

        Returns:
            dict: 包含新的 access_token 和 refresh_token
        """
        try:

            # 生成新的 tokens
            new_access_token = TokenService.create_access_token({"sub": username})
            new_refresh_token = TokenService.create_refresh_token({"sub": username})

            # 撤销旧的 refresh token
            await token_repo.revoke_refresh_token(old_jti)

            payload = TokenService.decode_token(new_refresh_token)
            # 存储新的 refresh token
            success = await token_repo.store_refresh_token(username, payload)
            if not success:
                logger.error(f"Failed to store new refresh token for user: {username}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to refresh token"
                )

            return {
                "access_token": new_access_token,
                "refresh_token": new_refresh_token
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Token refresh failed for user {username}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to refresh token"
            )

    @staticmethod
    def check_token_status(payload: dict) -> dict:

        username = payload.get("sub")
        jti = payload.get("token_id")
        exp = payload.get("exp")
        token_type = payload.get("type")
        expire_seconds = exp - datetime.now(timezone.utc).timestamp() if exp else 0

        if not username or not jti or not expire_seconds:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token payload",
                headers={"WWW-Authenticate": "Bearer"},
            )

        return {
            "username": username,
            "jti": jti,
            "token_type": token_type,
            "expire_seconds": expire_seconds,
        }



    @staticmethod
    async def verify_access_token_permissions(*, access_token: str, refresh_token: str = None, db, token_repo):
        """
        验证 Access Token 并获取用户权限

        Args:
            access_token: Access Token
            refresh_token: Refresh Token (可选)
            db: 数据库会话
            token_repo: Token 仓储

        Returns:
            List[str]: 用户权限名称列表
        """
        try:
            # 验证 access token 并获取用户信息
            username =await AuthService.get_current_user(access_token=access_token, refresh_token=refresh_token,token_repo=token_repo)

            # 直接调用 get_user_permission 函数获取用户权限
            # 注意：这里不能使用 Depends，需要直接调用函数
            permissions_objs = await AuthService.get_user_permission(username=username, db=db)
            permissions = [perm.name for perm in permissions_objs]

            logger.info(f"Retrieved {len(permissions)} permissions for user {username}")
            return permissions

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error verifying access token permissions: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Failed to verify token permissions",
                headers={"WWW-Authenticate": "Bearer"},
            )

    @staticmethod
    async def verify_api_token_scopes(api_token: str, db):
        """
        验证 API Token 并获取权限范围

        Args:
            api_token: API Token
            db: 数据库会话

        Returns:
            List[str]: API Token 的权限范围列表
        """
        try:
            token_info = await TokenService.verify_api_token(api_token=api_token, db=db)

            return token_info.get["scopes"]

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error verifying API token scopes: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Failed to verify API token scopes",
                headers={"WWW-Authenticate": "Bearer"},
            )

    # 依赖项，用于获取API权限
    @staticmethod
    async def get_api_scopes(api_path: str, db, token_repo):
        """
        从数据库获取API路径所需的权限范围（带Redis缓存）

        Args:
            api_path: API路径
            db: 数据库会话
            token_repo: Token仓储（用于获取Redis客户端）

        Returns:
            list[str]: 权限范围列表
        """


        # 尝试从Redis缓存获取
        if token_repo and token_repo.redis_client:
            try:
                cache_key = f"api_scope:{api_path}"
                cached_scopes = await token_repo.redis_client.get(cache_key)
                if cached_scopes:
                    logger.debug(f"Cache hit for API scopes: {api_path}")
                    return json.loads(cached_scopes.decode() if isinstance(cached_scopes, bytes) else cached_scopes)
            except Exception as e:
                logger.warning(f"Failed to get API scopes from cache for {api_path}: {e}")

        # 从数据库获取
        repo = ApiScopesRepository(db)
        api_scope = await repo.get_api_scope_by_path(api_path)

        # 如果找到配置，返回其权限范围
        if api_scope and api_scope.is_active:
            scopes = api_scope.required_scopes

            # 缓存结果到Redis（5分钟）
            if token_repo and token_repo.redis_client:
                try:
                    cache_key = f"api_scope:{api_path}"
                    await token_repo.redis_client.setex(cache_key, 300, json.dumps(scopes))
                    logger.debug(f"Cached API scopes for {api_path}: {scopes}")
                except Exception as e:
                    logger.warning(f"Failed to cache API scopes for {api_path}: {e}")

            return scopes

        # 默认返回空列表（无权限要求）
        # 也缓存空结果，避免重复查询不存在的API路径
        if token_repo and token_repo.redis_client:
            try:
                cache_key = f"api_scope:{api_path}"
                await token_repo.redis_client.setex(cache_key, 60, json.dumps([]))  # 空结果缓存1分钟
                logger.debug(f"Cached empty API scopes for {api_path}")
            except Exception as e:
                logger.warning(f"Failed to cache empty API scopes for {api_path}: {e}")

        return []




async def authenticate_token(*,
                             credentials: HTTPAuthorizationCredentials = Depends(security),
                             db: AsyncSessionDep,
                             token_repo: RefreshTokenRepository = Depends(get_refresh_token_repository),
                             refresh_token: Optional[str] = Cookie(None, alias="refresh_token")
                             ):
    token = credentials.credentials if credentials and credentials.scheme == "Bearer" else None

    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    try:
        payload = TokenService.decode_token(token)
        # 验证 token 类型
        token_type = payload.get("type")
        username = payload.get("sub")
        jti = payload.get("token_id")

        if not username or not jti:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 获取用户信息
        user_repo = UserRepository(db)
        user = await user_repo.get_one_by_username(username)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not exist",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 根据token类型获取权限和scopes
        permissions = []
        scopes = []

        if token_type == TokenType.ACCESS:
            permissions = await TokenService.verify_access_token_permissions(access_token=token, refresh_token=refresh_token, db=db, token_repo=token_repo)

        elif token_type == TokenType.API:
            scopes = await TokenService.verify_api_token_scopes(token, db)

        return AuthContext(
            username=username,
            token_type= TokenType(token_type),
            permissions=permissions,
            scopes=scopes,
            jti=jti
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Authentication failed, {e}",
            headers={"WWW-Authenticate": "Bearer"},
        )
