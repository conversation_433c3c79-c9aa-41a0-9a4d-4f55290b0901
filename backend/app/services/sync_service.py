"""
API Scopes Configuration Sync Tool

Read API scopes configuration from YAML files and sync to database
"""
import yaml
import json
from pathlib import Path
from typing import   Any
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession

from backend.app.repo.user_repository import ApiScopesRepository
from backend.app.config.env import BaseConfigSettings

async def sync_api_scopes_on_startup(db):
    """应用启动时同步api权限配置"""
    try:
        # 先验证配置文件
        logger.info("Validating API scopes configuration...")
        errors = await ApiScopeSyncService.validate_config()

        if errors:
            logger.error("API scopes configuration validation failed:")
            for error in errors:
                logger.error(f"  - {error}")
            raise Exception("API scopes configuration validation failed")

        logger.info("API scopes configuration validation passed")

        # 同步到数据库
        logger.info("Syncing API scopes to database...")
        stats = await ApiScopeSyncService.sync_yml_to_db(db)
        logger.success(f"API scopes sync completed: Created {stats['created']}, Updated {stats['updated']}, Deleted {stats['deleted']}, Deactivated {stats['deactivated']},Reactivated {stats['reactivated']}, Skipped {stats['skipped']}")

    except Exception as e:
        logger.error(f"Failed to sync API scopes on startup: {e}")
        # 根据需要决定是否要让应用启动失败
        # raise  # 如果权限配置是必需的，可以取消注释这行


class ApiScopeConfigLoader:
    """API Scopes Configuration Loader"""

    def __init__(self, config_path: str = None):
        if config_path is None:
            # 使用配置类中的 backend_path，确保路径正确
            self.config_path = BaseConfigSettings.backend_path / "app" / "config" / "api_scopes.yml"
        else:
            self.config_path = Path(config_path)

    def load_config(self) -> dict[str, Any]:
        """Load configuration file"""
        if not self.config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")

        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                if self.config_path.suffix.lower() in ['.yml', '.yaml']:
                    return yaml.safe_load(file)
                elif self.config_path.suffix.lower() == '.json':
                    return json.load(file)
                else:
                    raise ValueError(f"Unsupported configuration file format: {self.config_path.suffix}")
        except Exception as e:
            logger.error(f"Failed to load configuration file: {e}")
            raise

    def parse_scopes(self) -> list[dict[str, Any]]:
        """Parse scopes configuration"""
        config = self.load_config()
        scopes = []
        
        api_scopes = config.get('api_scopes', {})
        
        for category, apis in api_scopes.items():
            for api_config in apis:
                scope_data = {
                    'api_path': api_config['path'],
                    'description': api_config.get('description', ''),
                    'required_scopes': api_config.get('required_scopes', []),
                    'is_active': api_config.get('is_active', True),
                    'category': category  # Add category information
                }
                scopes.append(scope_data)
        
        return scopes


class ApiScopeSyncService:
    """API Scopes Sync Service"""

    def __init__(self, db: AsyncSession):
        """
        Initialize API scopes sync service

        Args:
            db: Async database session
        """
        if not db:
            raise ValueError("Database session cannot be None")

        self.db = db
        self.repo = ApiScopesRepository(db)
        self.loader = ApiScopeConfigLoader()

        # Cache configuration to avoid repeated loading
        self._config_cache = None
        self._config_loaded = False


    # todo 改为redis数据库
    def _get_cached_config(self) -> list[dict]:
        """
        Get cached configuration to avoid repeated parsing

        Returns:
            List of scopes configuration
        """
        if not self._config_loaded:
            self._config_cache = self.loader.parse_scopes()
            self._config_loaded = True
            logger.debug(f"Loaded {len(self._config_cache)} scopes from config")

        return self._config_cache

    # todo 改为redis数据库
    def clear_config_cache(self):
        """Clear configuration cache"""
        self._config_cache = None
        self._config_loaded = False
        logger.debug("Config cache cleared")

    async def get_sync_preview(self) -> dict:
        """
        Get sync preview without actually executing sync operations

        Returns:
            Dictionary containing sync operation preview
        """
        scopes_config = self._get_cached_config()
        existing_scopes = await self.repo.get_all_api_scopes()

        # Build lookup dictionaries
        existing_paths_dict = {scope.api_path: scope for scope in existing_scopes}
        config_dict = {scope['api_path']: scope for scope in scopes_config}

        # Use set operations to calculate required operations
        config_paths_set = set(config_dict.keys())
        existing_paths_set = set(existing_paths_dict.keys())
        existing_active_paths_set = {
            path for path, scope in existing_paths_dict.items()
            if scope.is_active
        }
        existing_inactive_paths_set = existing_paths_set - existing_active_paths_set

        # Calculate various operation sets
        paths_to_create = config_paths_set - existing_paths_set
        paths_to_deactivate = existing_active_paths_set - config_paths_set
        paths_to_reactivate = config_paths_set & existing_inactive_paths_set
        paths_potentially_update = config_paths_set & existing_active_paths_set
        paths_to_delete = existing_inactive_paths_set - config_paths_set

        return {
            "summary": {
                "total_config": len(config_paths_set),
                "total_existing": len(existing_paths_set),
                "total_active": len(existing_active_paths_set),
                "total_inactive": len(existing_inactive_paths_set)
            },
            "operations": {
                "create": list(paths_to_create),
                "deactivate": list(paths_to_deactivate),
                "reactivate": list(paths_to_reactivate),
                "potentially_update": list(paths_potentially_update),
                "delete": list(paths_to_delete)
            },
            "counts": {
                "create": len(paths_to_create),
                "deactivate": len(paths_to_deactivate),
                "reactivate": len(paths_to_reactivate),
                "potentially_update": len(paths_potentially_update),
                "delete": len(paths_to_delete)
            }
        }

    @classmethod
    async def sync_yml_to_db(cls, db: AsyncSession) -> dict[str, int]:
        """
        Sync scopes configuration to database
        Use set operations to optimize performance and add detailed judgment logic
        """
        try:
            service = cls(db)
            scopes_config = service._get_cached_config()  # Use cache
            existing_scopes = await service.repo.get_all_api_scopes()

            # Build lookup dictionaries
            existing_paths_dict = {scope.api_path: scope for scope in existing_scopes}
            config_dict = {scope['api_path']: scope for scope in scopes_config}

            stats = {'created': 0, 'updated': 0, 'deactivated': 0, 'reactivated': 0, 'deleted': 0, 'skipped': 0}

            # Use set operations to calculate required operations
            config_paths_set = set(config_dict.keys())
            existing_paths_set = set(existing_paths_dict.keys())
            existing_active_paths_set = {
                path for path, scope in existing_paths_dict.items()
                if scope.is_active
            }
            existing_inactive_paths_set = existing_paths_set - existing_active_paths_set

            # Set operations: calculate various operation sets
            paths_to_create = config_paths_set - existing_paths_set  # Create: in config but not in database
            paths_to_deactivate = existing_active_paths_set - config_paths_set  # Deactivate: active in database but not in config
            paths_to_reactivate = config_paths_set & existing_inactive_paths_set  # Reactivate: in config and inactive in database
            paths_potentially_update = config_paths_set & existing_active_paths_set  # Potentially update: in both config and database and active
            paths_to_delete = existing_inactive_paths_set - config_paths_set  # Delete: inactive in database and not in config

            logger.info(f"Sync analysis: create={len(paths_to_create)}, deactivate={len(paths_to_deactivate)}, "
                       f"reactivate={len(paths_to_reactivate)}, potentially_update={len(paths_potentially_update)}, "
                       f"delete={len(paths_to_delete)}")

            # 1. Batch create new scopes
            if paths_to_create:
                logger.info(f"Creating {len(paths_to_create)} new API scopes...")
                for api_path in paths_to_create:
                    await service.repo.create_api_scope(config_dict[api_path])
                    stats['created'] += 1
                    logger.debug(f"Created API scope: {api_path}")

            # 2. Batch deactivate scopes
            if paths_to_deactivate:
                logger.info(f"Deactivating {len(paths_to_deactivate)} API scopes...")
                for api_path in paths_to_deactivate:
                    await service.repo.mark_api_scope_is_active(api_path, False)
                    stats['deactivated'] += 1
                    logger.warning(f"Deactivated API scope: {api_path}")

            # 3. Batch reactivate scopes
            if paths_to_reactivate:
                logger.info(f"Reactivating {len(paths_to_reactivate)} API scopes...")
                for api_path in paths_to_reactivate:
                    # Reactivate and update configuration
                    await service.repo.mark_api_scope_is_active(api_path, True)
                    updated = await service.repo.update_api_scope(api_path, config_dict[api_path])
                    stats['reactivated'] += 1
                    logger.info(f"Reactivated and updated API scope: {api_path}")

            # 4. Batch delete inactive scopes that are not in config
            if paths_to_delete:
                logger.info(f"Deleting {len(paths_to_delete)} inactive API scopes...")
                for api_path in paths_to_delete:
                    success = await service.repo.delete_api_scope(api_path)
                    if success:
                        stats['deleted'] += 1
                        logger.info(f"Deleted API scope: {api_path}")
                    else:
                        stats['skipped'] += 1
                        logger.warning(f"Failed to delete API scope: {api_path}")

            # 5. Check and update existing active scopes (only update when really needed)
            if paths_potentially_update:
                logger.info(f"Checking {len(paths_potentially_update)} existing scopes for updates...")
                for api_path in paths_potentially_update:
                    existing_scope = existing_paths_dict[api_path]
                    config_scope = config_dict[api_path]

                    # Detailed comparison: only update when actual content differs
                    needs_update = (
                        existing_scope.description != config_scope.get('description') or
                        existing_scope.required_scopes != config_scope.get('required_scopes', []) or
                        existing_scope.category != config_scope.get('category', 'default') or
                        existing_scope.is_active != config_scope.get('is_active', True)
                    )

                    if needs_update:
                        updated = await service.repo.update_api_scope(api_path, config_scope)
                        if updated:
                            stats['updated'] += 1
                            logger.debug(f"Updated API scope: {api_path}")
                        else:
                            stats['skipped'] += 1
                            logger.debug(f"Update failed for API scope: {api_path}")
                    else:
                        stats['skipped'] += 1
                        logger.debug(f"No changes needed for API scope: {api_path}")

            # Don't commit here, let caller manage transaction uniformly
            logger.debug(f"API scopes sync completed - Created: {stats['created']}, "
                          f"Updated: {stats['updated']}, Deactivated: {stats['deactivated']}, "
                          f"Reactivated: {stats['reactivated']}, Deleted: {stats['deleted']}, Skipped: {stats['skipped']}")

            return stats

        except Exception as e:
            logger.error(f"API scopes sync failed: {e}")
            # No need to manually rollback, let caller manage transaction uniformly
            raise

    @classmethod
    async def validate_config(cls) -> list[str]:
        """
        Validate configuration file
        Use set operations to optimize duplicate checking and add more validation rules
        """
        errors = []
        loader = ApiScopeConfigLoader()

        try:
            scopes_config = loader.parse_scopes()

            if not scopes_config:
                errors.append("Configuration file is empty or has no scopes configuration")
                return errors

            # Use set operations to check duplicate API paths (performance optimization)
            paths = [scope['api_path'] for scope in scopes_config if scope.get('api_path')]
            paths_set = set(paths)

            if len(paths) != len(paths_set):
                # Find specific duplicates
                from collections import Counter
                path_counts = Counter(paths)
                duplicates = {path for path, count in path_counts.items() if count > 1}
                errors.append(f"Found duplicate API paths: {sorted(duplicates)}")

            # Batch validate required fields and data types
            required_fields = ['api_path', 'description']
            recommended_fields = ['required_scopes', 'category']  # Recommended but not required

            for i, scope in enumerate(scopes_config):
                scope_errors = []
                scope_warnings = []

                # Check required fields
                for field in required_fields:
                    if not scope.get(field):
                        scope_errors.append(f"Missing required field '{field}'")

                # Check recommended fields (warnings, not errors)
                for field in recommended_fields:
                    if field not in scope:
                        scope_warnings.append(f"Missing recommended field '{field}'")

                # Check API path format and business rules
                api_path = scope.get('api_path', '')
                if api_path and not api_path.startswith('/'):
                    scope_errors.append(f"API path must start with '/': {api_path}")

                # Business logic validation for sensitive APIs
                if api_path and any(sensitive in api_path.lower() for sensitive in ['admin', 'delete', 'remove', 'destroy']):
                    if not scope.get('required_scopes'):
                        scope_warnings.append(f"Sensitive API '{api_path}' should have required_scopes defined")

                # Check data types
                if 'required_scopes' in scope and not isinstance(scope['required_scopes'], list):
                    scope_errors.append(f"required_scopes must be list type")

                if 'category' in scope and not isinstance(scope['category'], str):
                    scope_errors.append(f"category must be string type")

                if 'is_active' in scope and not isinstance(scope['is_active'], bool):
                    scope_errors.append(f"is_active must be boolean type")

                # Check description quality
                description = scope.get('description', '')
                # if description:
                #     if len(description) > 500:
                #         scope_errors.append(f"Description too long (over 500 characters): {len(description)} characters")


                # Collect errors and warnings
                if scope_errors:
                    errors.append(f"Scopes configuration item {i+1} ({api_path}) errors: {'; '.join(scope_errors)}")

                if scope_warnings:
                    logger.warning(f"Scopes configuration item {i+1} ({api_path}) warnings: {'; '.join(scope_warnings)}")

            # Statistics
            if not errors:
                logger.info(f"Configuration validation passed: {len(scopes_config)} API scopes configurations")
            else:
                logger.warning(f"Configuration validation found {len(errors)} errors")

        except Exception as e:
            errors.append(f"Configuration file parsing error: {str(e)}")
            logger.error(f"Configuration file parsing failed: {e}")

        return errors

    @classmethod
    async def export_db_to_yaml(cls, db: AsyncSession, output_path: str = None) -> None:
        """Export scopes configuration from database to YAML file"""

        try:
            # 如果没有指定输出路径，使用默认路径
            if output_path is None:
                output_path = BaseConfigSettings.backend_path / "app" / "config" / "api_scopes_export.yml"

            service = cls(db)
            scopes = await service.repo.get_all_api_scopes()

            # Organize data by category field from database
            categories = {}
            for scope in scopes:
                # Use category field directly from database
                category = scope.category if scope.category else 'uncategorized'

                if category not in categories:
                    categories[category] = []

                categories[category].append({
                    'path': scope.api_path,
                    'description': scope.description,
                    'required_scopes': scope.required_scopes,
                    'is_active': scope.is_active
                })

            # Build YAML structure
            yaml_data = {'api_scopes': categories}

            # Write to file
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            with open(output_file, 'w', encoding='utf-8') as file:
                yaml.dump(yaml_data, file, default_flow_style=False, allow_unicode=True, indent=2)

            logger.info(f"Scopes configuration exported to: {output_path}")

        except Exception as e:
            logger.error(f"Failed to export scopes configuration: {e}")
            raise

