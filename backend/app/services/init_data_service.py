from loguru import logger

from sqlalchemy import insert
from backend.app.models import Permission, Role, User
from backend.app.models.user_models import associate_users_roles
from backend.app.config.enums import RoleType, PermissionType
from backend.app.repo.user_repository import PermissionRepository, RoleRepository, UserRepository
from backend.app.services.auth_service import AuthService
from backend.app.config.env import app_settings
from backend.app.services.sync_service import sync_api_scopes_on_startup

async def initialize_app(db):
    """
    应用初始化 - 接收外部传入的数据库会话

    Args:
        db: 数据库会话（从 server.py 的 lifespan 中传入）
    """
    logger.info("Starting application initialization...")

    try:
        # 1. 同步权限配置
        await sync_api_scopes_on_startup(db)

        # 2. 初始化基础数据（角色、权限、管理员用户）
        # 可以通过环境变量控制是否在启动时初始化
        if getattr(app_settings, 'app_init_base_data_on_startup', True):
            logger.info("Initializing base data (roles, permissions, admin user)...")

            result = await init_admin(db)

            if result["success"]:
                logger.success("Base data initialization completed successfully")

                # 记录详细信息
                if result.get("created"):
                    message = """
                    🔐 Default admin credentials created:
                       Username: admin
                       Password: admin
                       ⚠️ Please change the default password after first login!
                    """
                    logger.warning("\n" + message)
                else:
                    logger.info("Admin user already exists, skipping creation")

            else:
                logger.error("Base data initialization failed")
        else:
            logger.info("Base data initialization on startup is disabled")

        # 3. 可以在这里添加其他初始化任务
        # await init_other_components(db)

        logger.info("Application initialization completed")

    except Exception as e:
        logger.error(f"Application initialization failed: {e}")
        # 让错误向上传播，由调用者决定如何处理
        raise


async def init_roles(db):
    """
    初始化角色数据
    使用集合运算优化性能，只在需要时进行数据库操作
    """
    try:
        role_repo = RoleRepository(db)

        # 获取现有角色
        exist_roles_entity = await role_repo.get_all()
        exist_roles_set = {
            role.name.value if hasattr(role.name, 'value') else str(role.name)
            for role in exist_roles_entity
        }

        # 获取应该存在的角色
        valid_roles_set = {role.value for role in RoleType}

        # 使用集合运算计算需要的操作
        roles_to_create = valid_roles_set - exist_roles_set  # 差集：应该有但没有的
        roles_to_delete = exist_roles_set - valid_roles_set  # 差集：有但不应该有的

        stats = {'role_created': 0, 'role_deleted': 0, 'role_updated': 0, 'skipped': 0}

        # 批量创建角色
        for role_value in roles_to_create:
            role_type = next(role for role in RoleType if role.value == role_value)
            role_to_add = Role(name=role_type, description=role_type.description)
            await role_repo.create_entity(role_to_add)
            stats['role_created'] += 1
            logger.debug(f"Created role: {role_type.value} - {role_type.description}")

        # 检查并更新现有角色的描述
        for role_entity in exist_roles_entity:
            role_value = role_entity.name.value if hasattr(role_entity.name, 'value') else str(role_entity.name)
            if role_value in valid_roles_set:
                role_type = next(role for role in RoleType if role.value == role_value)
                # 只有描述不同时才更新
                if role_entity.description != role_type.description:
                    role_entity.description = role_type.description
                    stats['role_updated'] += 1
                    logger.debug(f"Updated role description: {role_type.value}")
                else:
                    stats['skipped'] += 1

        # 批量删除无效角色
        for role_value in roles_to_delete:
            role_to_delete = await role_repo.get_one_by_name(role_value)
            if role_to_delete:
                await role_repo.delete(role_to_delete.id)
                stats['role_deleted'] += 1
                logger.debug(f"Deleted invalid role: {role_value}")

        # 不在这里提交，由调用方统一管理事务

        logger.success(f"Role initialization completed - Created: {stats['role_created']}, Updated: {stats['role_updated']}, Deleted: {stats['role_deleted']}, Skipped: {stats['skipped']}")
        return stats

    except Exception as e:
        logger.error(f"Failed to initialize roles: {e}")
        # 不需要手动回滚，get_async_transaction_session() 会自动回滚
        raise e


async def init_permissions(db):
    """
    初始化权限数据
    使用集合运算优化性能，只在需要时进行数据库操作
    """
    try:
        permission_repo = PermissionRepository(db)

        # 获取现有权限
        exist_permissions_entity = await permission_repo.get_all()
        exist_permissions_set = {
            permission.name.value if hasattr(permission.name, 'value') else str(permission.name)
            for permission in exist_permissions_entity
        }

        # 获取应该存在的权限
        valid_permissions_set = {perm.value for perm in PermissionType}

        # 使用集合运算计算需要的操作
        permissions_to_create = valid_permissions_set - exist_permissions_set  # 差集：应该有但没有的
        permissions_to_delete = exist_permissions_set - valid_permissions_set  # 差集：有但不应该有的

        stats = {'permission_created': 0, 'permission_updated': 0, 'permission_deleted': 0, 'skipped': 0}

        # 批量创建权限
        for perm_value in permissions_to_create:
            permission_type = next(perm for perm in PermissionType if perm.value == perm_value)
            permission_to_add = Permission(name=permission_type, description=permission_type.description)
            await permission_repo.create_entity(permission_to_add)
            stats['permission_created'] += 1
            logger.debug(f"Created permission: {permission_type.value} - {permission_type.description}")

        # 检查并更新现有权限的描述
        for permission_entity in exist_permissions_entity:
            perm_value = permission_entity.name.value if hasattr(permission_entity.name, 'value') else str(permission_entity.name)
            if perm_value in valid_permissions_set:
                permission_type = next(perm for perm in PermissionType if perm.value == perm_value)
                # 只有描述不同时才更新
                if permission_entity.description != permission_type.description:
                    permission_entity.description = permission_type.description
                    stats['permission_updated'] += 1
                    logger.debug(f"Updated permission description: {permission_type.value}")
                else:
                    stats['skipped'] += 1

        # 批量删除无效权限
        for perm_value in permissions_to_delete:
            perm_to_delete = await permission_repo.get_one_by_name(perm_value)
            if perm_to_delete:
                await permission_repo.delete(perm_to_delete.id)
                stats['permission_deleted'] += 1
                logger.debug(f"Deleted invalid permission: {perm_value}")

        # 不在这里提交，由调用方统一管理事务

        logger.success(f"Permission initialization completed - Created: {stats['permission_created']}, Updated: {stats['permission_updated']}, Deleted: {stats['permission_deleted']}, Skipped: {stats['skipped']}")
        return stats

    except Exception as e:
        logger.error(f"Failed to initialize permissions: {e}")
        # 不需要手动回滚，get_async_transaction_session() 会自动回滚
        raise e

async def init_admin_role_permission(db):
    """
    为管理员角色分配所有权限
    使用集合运算优化性能，只分配缺失的权限
    """
    try:
        role_repo = RoleRepository(db)
        permission_repo = PermissionRepository(db)

        # 获取admin角色（预加载权限关系）
        admin_role = await role_repo.get_one_by_name_with_permissions("admin")

        if not admin_role:
            logger.error("Admin role does not exist, please initialize roles first")
            raise ValueError("Admin role does not exist, please initialize roles first")

        # 获取所有权限
        all_permissions = await permission_repo.get_all()

        # 使用集合运算找出需要添加的权限
        current_permission_ids = {perm.id for perm in admin_role.permissions}
        all_permission_ids = {perm.id for perm in all_permissions}
        missing_permission_ids = all_permission_ids - current_permission_ids

        stats = {'permissions_added': 0, 'permissions_skipped': 0}

        # 只添加缺失的权限
        for permission in all_permissions:
            if permission.id in missing_permission_ids:
                admin_role.permissions.append(permission)  # type: ignore
                stats['permissions_added'] += 1
                logger.debug(f"Added permission to admin role: {permission.name}")
            else:
                stats['permissions_skipped'] += 1

        # 不在这里提交，由调用方统一管理事务

        logger.success(f"Admin role permissions initialized - Added: {stats['permissions_added']}, Skipped: {stats['permissions_skipped']}")
        return {
            "success": True,
            "message": "Admin role permissions initialized successfully",
            "stats": stats
        }

    except Exception as e:
        logger.error(f"Failed to initialize admin role permissions: {e}")
        # 不需要手动回滚，get_async_transaction_session() 会自动回滚
        raise e


async def init_admin(db):
    """
    创建默认管理员用户
    用户名: admin
    密码: admin
    角色: ADMIN (拥有所有权限)
    """
    try:
        user_repo = UserRepository(db)
        role_repo = RoleRepository(db)

        # 检查管理员用户是否已存在
        admin_user = await user_repo.get_one_by_username("admin")

        if admin_user:
            logger.debug("Admin user already exists, skipping creation")
            return {"success": True, "message": "Admin user already exists", "created": False}

        # 获取 ADMIN 角色
        admin_role = await role_repo.get_one_by_name(RoleType.ADMIN.value)

        if not admin_role:
            logger.warning("ADMIN role does not exist. Initializing roles and permissions...")
            await init_roles(db)
            await init_permissions(db)
            await init_admin_role_permission(db)

            # 重新获取 ADMIN 角色
            admin_role = await role_repo.get_one_by_name(RoleType.ADMIN.value)

            if not admin_role:
                logger.error("Failed to create ADMIN role")
                raise ValueError("Failed to create ADMIN role")

        # 创建管理员用户
        hashed_password = AuthService.get_password_hash("admin")
        admin_user = User(
            username="admin",
            password=hashed_password
        )

        # 添加用户到数据库
        await user_repo.create_entity(admin_user)
        await db.flush()  # 刷新以获取用户ID

        # 为用户分配 ADMIN 角色（使用SQLAlchemy 2.0 insert语句）
        stmt = insert(associate_users_roles).values(
            user_id=admin_user.id,
            role_name=admin_role.name.value
        )
        await db.execute(stmt)
        
        # 不在这里提交，由调用方统一管理事务

        logger.success("Admin user created successfully - username: admin, password: admin")
        return {
            "success": True,
            "message": "Admin user created successfully",
            "created": True,
            "username": "admin",
            "roles": [admin_role.name.value if hasattr(admin_role.name, 'value') else str(admin_role.name)]
        }

    except Exception as e:
        logger.error(f"Failed to create admin user: {e}")
        # 不需要手动回滚，get_async_transaction_session() 会自动回滚
        raise e

