
# ===========================================
# 环境变量配置文件 (.env)
# 用于存储敏感信息和环境特定配置
# ===========================================

# ===========================================
# 应用程序配置（环境特定）
# ===========================================
# 注意：大部分应用配置已移至 config.yml
# 这里只保留环境特定的配置

# ===========================================
# 数据库配置（敏感信息）
# ===========================================
DB_HOST=127.0.0.1
DB_PORT=25432
DB_USERNAME=heygo
DB_PASSWORD=heygo01!
DB_DATABASE=heygo


# ===========================================
# Redis 配置（敏感信息）
# ===========================================
REDIS_HOST=127.0.0.1
REDIS_PORT=16379
REDIS_DB=0
REDIS_USERNAME=
REDIS_PASSWORD=

# ===========================================
# JWT 安全配置（敏感信息）
# ===========================================
JWT_SECRET_KEY=your-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_TIME=30
JWT_REFRESH_TOKEN_EXPIRE_TIME=10080

# ===========================================
# Notion API 配置（敏感信息）
# ===========================================
NOTION_API_KEY=
NOTION_DATABASE_ID=


