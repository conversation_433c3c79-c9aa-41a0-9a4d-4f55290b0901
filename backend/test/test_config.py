#!/usr/bin/env python3
"""
测试配置加载脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_config_loading():
    """测试配置加载"""
    try:
        # 首先测试路径
        from backend.app.config.env import BaseConfigSettings

        print("=" * 50)
        print("路径测试")
        print("=" * 50)
        print(f"项目根目录: {BaseConfigSettings.root_path}")
        print(f"Backend 目录: {BaseConfigSettings.backend_path}")
        print(f".env 文件路径: {BaseConfigSettings.backend_path / '.env'}")
        print(f"config.yml 文件路径: {BaseConfigSettings.backend_path / 'config.yml'}")

        # 检查文件是否存在
        env_file = BaseConfigSettings.backend_path / '.env'
        config_file = BaseConfigSettings.backend_path / 'config.yml'

        print(f".env 文件存在: {env_file.exists()}")
        print(f"config.yml 文件存在: {config_file.exists()}")

        if not env_file.exists():
            print(f"❌ .env 文件不存在: {env_file}")
        if not config_file.exists():
            print(f"❌ config.yml 文件不存在: {config_file}")

        print("\n" + "=" * 50)
        print("配置加载测试")
        print("=" * 50)

        from backend.app.config.env import (
            app_settings,
            jwt_settings,
            database_settings,
            redis_settings,
            notion_settings
        )
        
        print("=" * 50)
        print("配置加载测试")
        print("=" * 50)
        
        # 测试应用配置
        print(f"应用名称: {app_settings.app_name}")
        print(f"应用端口: {app_settings.app_port}")
        print(f"应用版本: {app_settings.app_version}")
        print(f"用户代理数量: {len(app_settings.app_user_agents)}")
        print(f"CORS 源: {app_settings.app_cors_origins}")
        
        print("\n" + "-" * 30)
        
        # 测试数据库配置
        print(f"数据库类型: {database_settings.db_type}")
        print(f"数据库主机: {database_settings.db_host}")
        print(f"数据库端口: {database_settings.db_port}")
        print(f"数据库名称: {database_settings.db_database}")
        print(f"连接池大小: {database_settings.db_pool_size}")
        
        print("\n" + "-" * 30)
        
        # 测试 Redis 配置
        print(f"Redis 主机: {redis_settings.redis_host}")
        print(f"Redis 端口: {redis_settings.redis_port}")
        print(f"Redis 数据库: {redis_settings.redis_db}")
        print(f"Redis 最大连接数: {redis_settings.redis_max_connections}")
        
        print("\n" + "-" * 30)
        
        # 测试 JWT 配置
        print(f"JWT 算法: {jwt_settings.jwt_algorithm}")
        print(f"访问令牌过期时间: {jwt_settings.jwt_access_token_expire_time} 分钟")
        print(f"刷新令牌过期时间: {jwt_settings.jwt_refresh_token_expire_time} 分钟")
        print(f"JWT 密钥长度: {len(jwt_settings.jwt_secret_key)} 字符")
        
        print("\n" + "-" * 30)
        
        # 测试 Notion 配置
        print(f"Notion 推送: {notion_settings.notion_push_to_notion}")
        print(f"Notion API Key: {'已设置' if notion_settings.notion_api_key else '未设置'}")
        print(f"Notion 数据库 ID: {'已设置' if notion_settings.notion_database_id else '未设置'}")
        
        print("\n" + "=" * 50)
        print("✅ 配置加载成功！")
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_config_loading()
