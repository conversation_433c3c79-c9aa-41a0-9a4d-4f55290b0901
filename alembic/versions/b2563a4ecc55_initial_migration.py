"""initial_migration

Revision ID: b2563a4ecc55
Revises: e98b8bee62fe
Create Date: 2025-07-31 19:33:05.995177

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'b2563a4ecc55'
down_revision: Union[str, Sequence[str], None] = 'e98b8bee62fe'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('api_scopes',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('api_path', sa.String(length=255), nullable=False, comment='API路径'),
    sa.Column('description', sa.Text(), nullable=True, comment='API描述'),
    sa.Column('required_scopes', postgresql.JSONB(astext_type=sa.Text()), nullable=False, comment='所需权限范围'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否启用'),
    sa.Column('category', sa.String(length=50), nullable=False, comment='API分类'),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    schema='auth'
    )
    op.create_index(op.f('ix_auth_api_scopes_api_path'), 'api_scopes', ['api_path'], unique=True, schema='auth')
    op.create_index(op.f('ix_auth_api_scopes_id'), 'api_scopes', ['id'], unique=False, schema='auth')
    op.create_table('associate_token_api_scopes',
    sa.Column('token_id', sa.Integer(), nullable=False),
    sa.Column('api_scope_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['api_scope_id'], ['auth.api_scopes.id'], ),
    sa.ForeignKeyConstraint(['token_id'], ['auth.user_tokens.id'], ),
    sa.PrimaryKeyConstraint('token_id', 'api_scope_id'),
    schema='auth'
    )
    op.drop_table('associate_token_api_permissions', schema='auth')
    op.drop_index(op.f('ix_auth_api_permissions_api_path'), table_name='api_permissions', schema='auth')
    op.drop_index(op.f('ix_auth_api_permissions_id'), table_name='api_permissions', schema='auth')
    op.drop_table('api_permissions', schema='auth')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('api_permissions',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('auth.api_permissions_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('api_path', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('required_scopes', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=False),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('category', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='api_permissions_pkey'),
    schema='auth',
    postgresql_ignore_search_path=False
    )
    op.create_index(op.f('ix_auth_api_permissions_id'), 'api_permissions', ['id'], unique=False, schema='auth')
    op.create_index(op.f('ix_auth_api_permissions_api_path'), 'api_permissions', ['api_path'], unique=True, schema='auth')
    op.create_table('associate_token_api_permissions',
    sa.Column('token_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('api_permission_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['api_permission_id'], ['auth.api_permissions.id'], name=op.f('associate_token_api_permissions_api_permission_id_fkey')),
    sa.ForeignKeyConstraint(['token_id'], ['auth.user_tokens.id'], name=op.f('associate_token_api_permissions_token_id_fkey')),
    sa.PrimaryKeyConstraint('token_id', 'api_permission_id', name=op.f('associate_token_api_permissions_pkey')),
    schema='auth'
    )
    op.drop_table('associate_token_api_scopes', schema='auth')
    op.drop_index(op.f('ix_auth_api_scopes_id'), table_name='api_scopes', schema='auth')
    op.drop_index(op.f('ix_auth_api_scopes_api_path'), table_name='api_scopes', schema='auth')
    op.drop_table('api_scopes', schema='auth')
    # ### end Alembic commands ###
