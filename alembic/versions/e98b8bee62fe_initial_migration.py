"""initial_migration

Revision ID: e98b8bee62fe
Revises:
Create Date: 2025-07-10 10:03:52.503138

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = 'e98b8bee62fe'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema - Create initial tables in auth schema."""

    # 1. Create auth schema
    op.execute("CREATE SCHEMA IF NOT EXISTS auth")

    # 2. Create users table
    op.create_table('users',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('username', sa.String(length=255), nullable=False),
        sa.Column('password', sa.String(length=255), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        schema='auth'
    )
    op.create_index('ix_auth_users_id', 'users', ['id'], schema='auth')
    op.create_index('ix_auth_users_username', 'users', ['username'], unique=True, schema='auth')

    # 3. Create roles table
    op.create_table('roles',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('name', sa.Enum('ADMIN', 'USER', 'GUEST', name='roletype', native_enum=False), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        schema='auth'
    )
    op.create_index('ix_auth_roles_id', 'roles', ['id'], schema='auth')
    op.create_index('ix_auth_roles_name', 'roles', ['name'], unique=True, schema='auth')

    # 4. Create permissions table
    op.create_table('permissions',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('name', sa.Enum('manage_users', 'manage_roles', 'manage_permissions', 'manage_tokens', 'manage_api_permissions', 'download_file', 'upload_file', 'write_database', 'read_database', 'delete_record', 'create_user', 'update_profile', 'modify_settings', 'create_record', 'administrator', name='permissiontype', native_enum=False), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        schema='auth'
    )
    op.create_index('ix_auth_permissions_id', 'permissions', ['id'], schema='auth')
    op.create_index('ix_auth_permissions_name', 'permissions', ['name'], unique=True, schema='auth')

    # 5. Create user_tokens table
    op.create_table('user_tokens',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('token_name', sa.String(length=255), nullable=False),
        sa.Column('expires_at', sa.DateTime(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('token', sa.Text(), nullable=False),
        sa.Column('permissions', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('api_paths', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['auth.users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        schema='auth'
    )
    op.create_index('ix_auth_user_tokens_id', 'user_tokens', ['id'], schema='auth')
    op.create_index('ix_auth_user_tokens_token', 'user_tokens', ['token'], unique=True, schema='auth')
    op.create_index('ix_auth_user_tokens_token_name', 'user_tokens', ['token_name'], unique=True, schema='auth')

    # 6. Create api_permissions table
    op.create_table('api_permissions',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('api_path', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('required_scopes', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('category', sa.String(length=50), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        schema='auth'
    )
    op.create_index('ix_auth_api_permissions_id', 'api_permissions', ['id'], schema='auth')
    op.create_index('ix_auth_api_permissions_api_path', 'api_permissions', ['api_path'], unique=True, schema='auth')

    # 7. Create association tables
    op.create_table('associate_roles_permissions',
        sa.Column('role_name', sa.Enum('ADMIN', 'USER', 'GUEST', name='roletype', native_enum=False), nullable=False),
        sa.Column('permission_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['permission_id'], ['auth.permissions.id'], ),
        sa.ForeignKeyConstraint(['role_name'], ['auth.roles.name'], ),
        sa.PrimaryKeyConstraint('role_name', 'permission_id'),
        schema='auth'
    )

    op.create_table('associate_users_roles',
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('role_name', sa.Enum('ADMIN', 'USER', 'GUEST', name='roletype', native_enum=False), nullable=False),
        sa.ForeignKeyConstraint(['role_name'], ['auth.roles.name'], ),
        sa.ForeignKeyConstraint(['user_id'], ['auth.users.id'], ),
        sa.PrimaryKeyConstraint('user_id', 'role_name'),
        schema='auth'
    )

    op.create_table('associate_token_api_permissions',
        sa.Column('token_id', sa.Integer(), nullable=False),
        sa.Column('api_permission_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['api_permission_id'], ['auth.api_permissions.id'], ),
        sa.ForeignKeyConstraint(['token_id'], ['auth.user_tokens.id'], ),
        sa.PrimaryKeyConstraint('token_id', 'api_permission_id'),
        schema='auth'
    )


def downgrade() -> None:
    """Downgrade schema - Drop all tables and auth schema."""
    # Drop association tables first
    op.drop_table('associate_token_api_permissions', schema='auth')
    op.drop_table('associate_users_roles', schema='auth')
    op.drop_table('associate_roles_permissions', schema='auth')

    # Drop main tables in reverse order
    op.drop_table('api_permissions', schema='auth')
    op.drop_table('user_tokens', schema='auth')
    op.drop_table('permissions', schema='auth')
    op.drop_table('roles', schema='auth')
    op.drop_table('users', schema='auth')

    # Drop auth schema if empty
    op.execute("DROP SCHEMA IF EXISTS auth")
