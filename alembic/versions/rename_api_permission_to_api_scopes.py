"""rename api_permission to api_scopes

Revision ID: rename_api_permission_to_api_scopes
Revises: 
Create Date: 2025-07-31 19:30:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'rename_api_permission_to_api_scopes'
down_revision: Union[str, None] = None  # 请根据实际情况修改为上一个版本的 revision
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """升级：将 api_permissions 重命名为 api_scopes"""
    
    # 检查表是否存在的辅助函数
    connection = op.get_bind()
    
    # 检查 api_permissions 表是否存在
    api_permissions_exists = connection.execute(sa.text("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'auth' 
            AND table_name = 'api_permissions'
        )
    """)).scalar()
    
    # 检查 api_scopes 表是否存在
    api_scopes_exists = connection.execute(sa.text("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'auth' 
            AND table_name = 'api_scopes'
        )
    """)).scalar()
    
    if not api_permissions_exists and not api_scopes_exists:
        # 两个表都不存在，创建新的 api_scopes 表
        op.create_table(
            'api_scopes',
            sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
            sa.Column('api_path', sa.String(length=255), nullable=False),
            sa.Column('description', sa.Text(), nullable=True),
            sa.Column('required_scopes', sa.JSON(), nullable=False),
            sa.Column('is_active', sa.Boolean(), nullable=False),
            sa.Column('category', sa.String(length=50), nullable=False),
            sa.Column('created_at', sa.DateTime(), nullable=False),
            sa.Column('updated_at', sa.DateTime(), nullable=False),
            sa.PrimaryKeyConstraint('id'),
            sa.UniqueConstraint('api_path'),
            schema='auth'
        )
        
        # 创建索引
        op.create_index('ix_auth_api_scopes_id', 'api_scopes', ['id'], unique=False, schema='auth')
        op.create_index('ix_auth_api_scopes_api_path', 'api_scopes', ['api_path'], unique=True, schema='auth')
        
        print("✅ 创建了新的 api_scopes 表")
        
    elif api_permissions_exists and not api_scopes_exists:
        # api_permissions 存在但 api_scopes 不存在，执行重命名
        print("🔄 开始重命名 api_permissions → api_scopes...")
        
        # 1. 重命名主表
        op.rename_table('api_permissions', 'api_scopes', schema='auth')
        print("✅ 主表重命名完成")
        
        # 2. 检查并处理关联表
        assoc_old_exists = connection.execute(sa.text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'auth' 
                AND table_name = 'associate_token_api_permissions'
            )
        """)).scalar()
        
        assoc_new_exists = connection.execute(sa.text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'auth' 
                AND table_name = 'associate_token_api_scopes'
            )
        """)).scalar()
        
        if assoc_old_exists and not assoc_new_exists:
            # 重命名关联表
            op.rename_table('associate_token_api_permissions', 'associate_token_api_scopes', schema='auth')
            
            # 重命名外键列
            op.alter_column('associate_token_api_scopes', 'api_permission_id', 
                          new_column_name='api_scope_id', schema='auth')
            
            print("✅ 关联表重命名完成")
        
        # 3. 更新索引名称（如果需要）
        try:
            # 重命名索引
            op.execute(sa.text("""
                ALTER INDEX IF EXISTS auth.ix_auth_api_permissions_id 
                RENAME TO ix_auth_api_scopes_id
            """))
            
            op.execute(sa.text("""
                ALTER INDEX IF EXISTS auth.ix_auth_api_permissions_api_path 
                RENAME TO ix_auth_api_scopes_api_path
            """))
            
            print("✅ 索引重命名完成")
        except Exception as e:
            print(f"⚠️ 索引重命名失败（可能不存在）: {e}")
        
        # 4. 更新外键约束名称（如果存在）
        try:
            # 查找并重命名外键约束
            fk_result = connection.execute(sa.text("""
                SELECT constraint_name 
                FROM information_schema.table_constraints 
                WHERE table_schema = 'auth' 
                AND table_name = 'associate_token_api_scopes'
                AND constraint_type = 'FOREIGN KEY'
                AND constraint_name LIKE '%api_permission%'
            """)).fetchall()
            
            for (constraint_name,) in fk_result:
                new_constraint_name = constraint_name.replace('api_permission', 'api_scope')
                op.execute(sa.text(f"""
                    ALTER TABLE auth.associate_token_api_scopes 
                    RENAME CONSTRAINT {constraint_name} TO {new_constraint_name}
                """))
                print(f"✅ 外键约束重命名: {constraint_name} → {new_constraint_name}")
                
        except Exception as e:
            print(f"⚠️ 外键约束重命名失败（可能不需要）: {e}")
        
        print("🎉 迁移完成！")
        
    elif api_scopes_exists:
        print("ℹ️ api_scopes 表已存在，跳过迁移")
    
    else:
        print("⚠️ 未知状态，请手动检查数据库")


def downgrade() -> None:
    """降级：将 api_scopes 重命名回 api_permissions"""
    
    connection = op.get_bind()
    
    # 检查 api_scopes 表是否存在
    api_scopes_exists = connection.execute(sa.text("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'auth' 
            AND table_name = 'api_scopes'
        )
    """)).scalar()
    
    # 检查 api_permissions 表是否存在
    api_permissions_exists = connection.execute(sa.text("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'auth' 
            AND table_name = 'api_permissions'
        )
    """)).scalar()
    
    if api_scopes_exists and not api_permissions_exists:
        print("🔄 开始回滚 api_scopes → api_permissions...")
        
        # 1. 回滚关联表
        assoc_exists = connection.execute(sa.text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'auth' 
                AND table_name = 'associate_token_api_scopes'
            )
        """)).scalar()
        
        if assoc_exists:
            # 回滚外键列名
            op.alter_column('associate_token_api_scopes', 'api_scope_id', 
                          new_column_name='api_permission_id', schema='auth')
            
            # 回滚关联表名
            op.rename_table('associate_token_api_scopes', 'associate_token_api_permissions', schema='auth')
            print("✅ 关联表回滚完成")
        
        # 2. 回滚主表
        op.rename_table('api_scopes', 'api_permissions', schema='auth')
        print("✅ 主表回滚完成")
        
        # 3. 回滚索引名称
        try:
            op.execute(sa.text("""
                ALTER INDEX IF EXISTS auth.ix_auth_api_scopes_id 
                RENAME TO ix_auth_api_permissions_id
            """))
            
            op.execute(sa.text("""
                ALTER INDEX IF EXISTS auth.ix_auth_api_scopes_api_path 
                RENAME TO ix_auth_api_permissions_api_path
            """))
            
            print("✅ 索引回滚完成")
        except Exception as e:
            print(f"⚠️ 索引回滚失败: {e}")
        
        # 4. 回滚外键约束名称
        try:
            fk_result = connection.execute(sa.text("""
                SELECT constraint_name 
                FROM information_schema.table_constraints 
                WHERE table_schema = 'auth' 
                AND table_name = 'associate_token_api_permissions'
                AND constraint_type = 'FOREIGN KEY'
                AND constraint_name LIKE '%api_scope%'
            """)).fetchall()
            
            for (constraint_name,) in fk_result:
                new_constraint_name = constraint_name.replace('api_scope', 'api_permission')
                op.execute(sa.text(f"""
                    ALTER TABLE auth.associate_token_api_permissions 
                    RENAME CONSTRAINT {constraint_name} TO {new_constraint_name}
                """))
                print(f"✅ 外键约束回滚: {constraint_name} → {new_constraint_name}")
                
        except Exception as e:
            print(f"⚠️ 外键约束回滚失败: {e}")
        
        print("🔄 回滚完成！")
        
    elif api_permissions_exists:
        print("ℹ️ api_permissions 表已存在，跳过回滚")
    else:
        print("⚠️ 没有找到可回滚的表")
