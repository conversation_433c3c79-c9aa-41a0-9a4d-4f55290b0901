# Greenlet 错误修复指南

## 错误描述

```
greenlet_spawn has not been called; can't call await_only() here. 
Was <PERSON><PERSON> attempted in an unexpected place? 
(Background on this error at: https://sqlalche.me/e/20/xd2s)
```

## 错误原因

这个错误通常发生在 SQLAlchemy 异步操作没有在正确的异步上下文中执行时。主要原因包括：

1. **异步上下文管理器问题**：使用了不兼容的会话管理方式
2. **数据库引擎配置问题**：缺少必要的异步配置参数
3. **会话创建时机问题**：在数据库初始化完成前就尝试创建会话

## 解决方案

### 1. 使用标准的异步会话

**问题代码：**
```python
# ❌ 可能导致 greenlet 错误
async with get_async_transaction_session() as db:
    await some_operation(db)
```

**修复后：**
```python
# ✅ 使用标准的异步会话
async for db in get_async_session():
    try:
        await some_operation(db)
        await db.commit()  # 手动提交
    except Exception as e:
        await db.rollback()  # 手动回滚
        raise
    break  # 只使用一个会话
```

### 2. 优化数据库引擎配置

**修复前：**
```python
async_engine = create_async_engine(
    settings.DATABASE_URL,
    echo=False,
    pool_pre_ping=True,
    future=True
)
```

**修复后：**
```python
async_engine = create_async_engine(
    settings.DATABASE_URL,
    echo=False,
    pool_pre_ping=True,
    future=True,
    # 添加异步相关配置
    pool_size=20,
    max_overflow=0,
    pool_recycle=3600,
    # 确保正确的异步上下文
    connect_args={
        "server_settings": {
            "application_name": "FastAPI_Auth_App",
        }
    }
)
```

### 3. 优化会话工厂配置

**修复后：**
```python
AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    expire_on_commit=False,
    class_=AsyncSession,
    # 确保异步上下文正确
    autoflush=True,
    autocommit=False
)
```

### 4. 添加初始化延迟

在应用启动时，在数据库初始化和会话创建之间添加短暂延迟：

```python
# 1. 初始化数据库
await init_db()

# 2. 初始化Redis
redis_client = await init_redis()

# 3. 等待确保数据库初始化完全完成
import asyncio
await asyncio.sleep(0.1)

# 4. 创建数据库会话
async for db in get_async_session():
    # 应用初始化...
    break
```

### 5. 移除多余的手动回滚

由于使用标准会话，需要手动管理事务：

```python
# 移除所有初始化函数中的自动回滚
async def init_roles(db):
    try:
        # 初始化逻辑...
        # 不在这里提交，由调用方统一管理事务
        return stats
    except Exception as e:
        logger.error(f"Failed to initialize roles: {e}")
        # 不需要手动回滚，由调用方处理
        raise e
```

## 修复的文件

### 1. app/main.py
- 使用 `get_async_session()` 替代 `get_async_transaction_session()`
- 添加初始化延迟
- 手动管理事务提交和回滚

### 2. app/db/database.py
- 优化异步引擎配置
- 添加连接池参数
- 优化会话工厂配置

### 3. app/core/init_data.py
- 移除所有函数中的手动回滚
- 让调用方统一管理事务

### 4. scripts/init_admin.py
- 使用标准异步会话
- 手动管理事务

## 诊断工具

创建了 `scripts/debug_greenlet_error.py` 脚本来诊断问题：

```bash
python scripts/debug_greenlet_error.py
```

这个脚本会逐步测试：
1. 基本数据库连接
2. 会话创建
3. 简单查询
4. 仓储类创建
5. 数据库操作

## 预防措施

1. **使用标准会话模式**：优先使用 `async for db in get_async_session():`
2. **正确的事务管理**：在最高层统一管理提交和回滚
3. **避免嵌套事务**：不要在初始化函数内部提交事务
4. **适当的延迟**：确保数据库完全初始化后再创建会话
5. **监控日志**：关注 SQLAlchemy 的异步上下文警告

## 测试验证

修复后，应用启动时应该看到：

```
2025-07-31 18:31:59 | INFO     | Starting application initialization...
2025-07-31 18:31:59 | INFO     | Initializing base data (roles, permissions, admin user)...
2025-07-31 18:31:59 | SUCCESS  | Admin user created successfully
2025-07-31 18:31:59 | INFO     | Application initialization transaction committed
2025-07-31 18:31:59 | SUCCESS  | FastAPI_Auth started successfully
```

而不是 greenlet 错误。

## 注意事项

1. **性能影响**：使用标准会话可能略微影响性能，但提高了稳定性
2. **事务管理**：需要更仔细地管理事务边界
3. **错误处理**：需要在适当的地方添加回滚逻辑
4. **兼容性**：这种方法与 FastAPI 的依赖注入系统更兼容
