# 管理员用户初始化指南

本文档介绍如何初始化系统的默认管理员用户和基础数据。

## 概述

系统提供了多种方式来初始化管理员用户和基础数据：

1. **脚本方式** - 使用独立的 Python 脚本
2. **API 方式** - 通过 REST API 接口
3. **程序化方式** - 在代码中直接调用初始化函数

## 默认管理员用户信息

- **用户名**: `admin`
- **密码**: `admin`
- **角色**: `ADMIN` (拥有所有权限)

⚠️ **安全提醒**: 请在首次登录后立即更改默认密码！

## 方法一：使用脚本初始化（推荐）

### 1. 完整初始化

运行以下命令来初始化所有基础数据和管理员用户：

```bash
python scripts/init_admin.py
```

这个脚本会：
- 创建所有系统角色（ADMIN, USER, TEST）
- 创建所有系统权限
- 为 ADMIN 角色分配所有权限
- 创建默认管理员用户

### 2. 测试管理员登录

初始化完成后，可以运行以下命令测试管理员用户是否创建成功：

```bash
python scripts/test_admin_login.py
```

## 方法二：使用 API 初始化

启动 FastAPI 应用后，可以通过以下 API 端点进行初始化：

### 1. 完整初始化

```http
POST /v1/admin/init/all
```

### 2. 分步初始化

如果需要分步进行，可以按顺序调用以下接口：

```http
# 1. 初始化角色
POST /v1/admin/init/roles

# 2. 初始化权限
POST /v1/admin/init/permissions

# 3. 为管理员角色分配权限
POST /v1/admin/init/admin-role-permissions

# 4. 创建管理员用户
POST /v1/admin/init/admin-user
```

## 方法三：程序化初始化

在代码中可以直接调用初始化函数：

```python
from backend.app.services.init_data_service import init_all_data
from backend.app import get_async_session


async def initialize_system():
   async for db in get_async_session():
      try:
         result = await init_all_data(db)
         print(f"初始化结果: {result}")
      finally:
         await db.close()
         break
```

## 初始化内容详情

### 角色 (Roles)

系统会创建以下角色：

- **ADMIN** - 系统管理员，拥有所有权限
- **USER** - 普通用户，拥有基本权限
- **TEST** - 测试用户，用于测试环境

### 权限 (Permissions)

系统会创建以下权限：

- `manage_users` - 管理用户
- `manage_roles` - 管理角色
- `manage_permissions` - 管理权限
- `manage_tokens` - 管理令牌
- `manage_api_permissions` - 管理API权限
- `download_file` - 下载文件
- `upload_file` - 上传文件
- `write_database` - 写入数据库
- `read_database` - 读取数据库
- `delete_record` - 删除记录
- `create_user` - 创建用户
- `update_profile` - 更新个人资料
- `modify_settings` - 修改设置
- `create_record` - 创建记录
- `administrator` - 超级管理员

### 角色权限关系

- **ADMIN** 角色会被分配所有权限
- 其他角色的权限需要根据业务需求手动分配

## 性能优化

### 集合运算优化

初始化函数使用了集合运算来优化性能：

```python
# 使用集合运算而不是列表推导式
existing_roles = {role.name.value for role in existing_entities}
valid_roles = {role.value for role in RoleType}

# 高效计算需要的操作
roles_to_create = valid_roles - existing_roles  # 差集
roles_to_delete = existing_roles - valid_roles  # 差集
roles_to_keep = existing_roles & valid_roles     # 交集
```

### 智能更新

- 只有在数据真正需要更新时才执行数据库操作
- 比较描述字段，只更新有变化的记录
- 避免不必要的数据库写入操作

### 性能测试

运行性能测试脚本：

```bash
python scripts/test_init_performance.py
```

## 注意事项

1. **数据库迁移**: 确保在初始化前已经运行了数据库迁移：
   ```bash
   alembic upgrade head
   ```

2. **幂等性**: 初始化脚本和 API 都支持重复执行，具有幂等性：
   - 不会创建重复数据
   - 只更新有变化的数据
   - 可以安全地多次运行

3. **错误处理**: 如果初始化过程中出现错误，会自动回滚事务

4. **日志记录**: 所有初始化操作都会记录详细的日志信息，包括：
   - 创建的记录数量
   - 更新的记录数量
   - 删除的记录数量
   - 跳过的记录数量

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置是否正确
   - 确保数据库服务正在运行

2. **表不存在错误**
   - 运行数据库迁移：`alembic upgrade head`

3. **权限错误**
   - 确保数据库用户有足够的权限创建表和插入数据

4. **重复键错误**
   - 这通常是正常的，表示数据已经存在，可以忽略

### 重置数据

如果需要重置所有数据，可以：

1. 删除数据库中的所有表
2. 重新运行迁移：`alembic upgrade head`
3. 重新运行初始化脚本

## 安全建议

1. **更改默认密码**: 首次登录后立即更改管理员密码
2. **限制访问**: 在生产环境中限制对初始化 API 的访问
3. **定期审计**: 定期检查用户权限和角色分配
4. **日志监控**: 监控管理员用户的操作日志
