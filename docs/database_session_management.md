# 数据库会话管理架构

## 概述

本文档描述了应用启动时数据库会话的统一管理架构，避免了在每个初始化函数中重复创建数据库会话的问题。

## 架构设计

### 优化前的问题

```python
# 每个函数都要创建自己的数据库会话 ❌
async def init_roles():
    async for db in get_async_session():
        # 初始化逻辑
        break

async def init_permissions():
    async for db in get_async_session():
        # 初始化逻辑
        break

async def init_admin_user():
    async for db in get_async_session():
        # 初始化逻辑
        break
```

**问题：**
- 重复的数据库会话创建代码
- 资源浪费（多个数据库连接）
- 代码维护困难
- 事务管理复杂

### 优化后的架构

```python
# 最优的数据库会话管理 ✅
# server.py - 在应用生命周期中统一管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 初始化数据库和Redis
    await init_db()
    redis_client = await init_redis()
    app.state.redis = redis_client

    # 在最顶层创建数据库会话
    async for db in get_async_session():
        try:
            await initialize_app(db)  # 传递会话
        except Exception as e:
            logger.error(f"Initialization failed: {e}")
        finally:
            break  # 只使用一个数据库会话

    yield
    # 清理资源...

# startup.py - 接收数据库会话
async def initialize_app(db):
    """接收外部传入的数据库会话"""
    await sync_permissions_on_startup(db)
    await init_admin_on_startup(db)

async def init_admin_on_startup(db):
    """所有函数都接收数据库会话参数"""
    result = await init_all_data(db)

async def init_all_data(db):
    """统一的初始化入口"""
    await init_roles(db)
    await init_permissions(db)
    await init_admin_role_permission(db)
    await init_admin_user(db)
```

## 优势

### 1. 资源效率
- **单一数据库连接**: 整个初始化过程只使用一个数据库会话
- **减少连接开销**: 避免重复创建和销毁数据库连接
- **内存优化**: 减少同时存在的数据库连接数量

### 2. 事务一致性
- **统一事务管理**: 所有初始化操作在同一个事务中
- **原子性保证**: 要么全部成功，要么全部回滚
- **数据一致性**: 避免部分初始化成功的情况

### 3. 代码维护性
- **DRY原则**: 消除重复的数据库会话创建代码
- **集中管理**: 数据库会话的生命周期在一个地方管理
- **易于调试**: 简化了数据库连接的调试过程

### 4. 错误处理
- **统一错误处理**: 在一个地方处理所有数据库相关错误
- **优雅降级**: 初始化失败不会阻止应用启动
- **详细日志**: 集中的日志记录和错误报告

## 实现细节

### 启动流程

```mermaid
graph TD
    A[应用启动] --> B[initialize_app]
    B --> C[创建数据库会话]
    C --> D[sync_permissions_on_startup]
    D --> E[init_admin_on_startup]
    E --> F[init_all_data]
    F --> G[init_roles]
    G --> H[init_permissions]
    H --> I[init_admin_role_permission]
    I --> J[init_admin_user]
    J --> K[提交事务]
    K --> L[关闭数据库会话]
    L --> M[应用启动完成]
```

### 函数签名变化

```python
# 优化前
async def init_roles(db: AsyncSessionDep):  # 使用依赖注入
async def init_permissions(db: AsyncSessionDep):

# 优化后
async def init_roles(db):  # 直接接收数据库会话参数
async def init_permissions(db):
```

### 配置控制

```python
# 通过配置控制是否在启动时初始化
INIT_ADMIN_ON_STARTUP = True  # 环境变量或配置文件

if getattr(settings, 'INIT_ADMIN_ON_STARTUP', True):
    await init_admin_on_startup(db)
else:
    logger.info("Admin initialization on startup is disabled")
```

## 使用示例

### 应用启动时自动初始化

```python
# server.py - 在应用生命周期的最顶层管理数据库会话
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 基础设施初始化
    await init_db()
    redis_client = await init_redis()
    app.state.redis = redis_client

    # 数据库会话管理 - 最顶层创建
    async for db in get_async_session():
        try:
            await initialize_app(db)  # 传递会话给所有初始化函数
        except Exception as e:
            logger.error(f"Application initialization failed: {e}")
        finally:
            break  # 只使用一个数据库会话

    yield
    # 关闭时清理资源...
```

### 独立脚本使用

```python
# scripts/init_admin.py
async def main():
    async for db in get_async_session():
        try:
            result = await init_all_data(db)  # 传递数据库会话
            # 处理结果...
        finally:
            break  # 只使用一个会话
```

### API 端点使用

```python
# API 端点仍然使用依赖注入
@router.post("/init/all")
async def initialize_all_data(db: AsyncSessionDep):
    result = await init_all_data(db)
    return result
```

## 最佳实践

1. **统一会话管理**: 在顶层函数中创建和管理数据库会话
2. **参数传递**: 将数据库会话作为参数传递给子函数
3. **错误处理**: 在顶层统一处理数据库相关错误
4. **事务管理**: 在适当的层级提交或回滚事务
5. **资源清理**: 确保数据库会话被正确关闭

## 注意事项

1. **会话生命周期**: 确保数据库会话在适当的时候被关闭
2. **事务边界**: 明确事务的开始和结束点
3. **错误传播**: 适当地处理和传播数据库错误
4. **并发考虑**: 避免在多个协程中共享同一个数据库会话
